import 'package:flutter/material.dart';

import '../../../localization/repositories/locale_repository.dart';
import '../../../localization/repositories/strings.dart';
import '../../../utils/extensions.dart';
import '../../../utils/validators.dart';
import '../models/item.dart';
import '../models/tip.dart';
import '../repositories/item_repository.dart';
import '../repositories/tip_repository.dart';
import 'item_definition_state.dart';

class ItemDefinitionStateImplementation extends ChangeNotifier
    implements ItemDefinitionState {
  ItemDefinitionStateImplementation({
    required ItemRepository itemRepository,
    required TipRepository tipRepository,
    required LocaleRepository localeRepository,
    required Validators validators,
  }) : _itemRepository = itemRepository,
       _tipRepository = tipRepository,
       _localeRepository = localeRepository,
       _validators = validators {
    _loadItemList();
    _loadTip();
    _localeRepository.addListener(() => notifyListeners());
  }

  final ItemRepository _itemRepository;
  final TipRepository _tipRepository;
  final LocaleRepository _localeRepository;
  final Validators _validators;

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  AutovalidateMode _autovalidateMode = AutovalidateMode.disabled;

  @override
  Strings get strings => _localeRepository.strings;

  @override
  GlobalKey<FormState> get formKey => _formKey;

  @override
  AutovalidateMode get autovalidateMode => _autovalidateMode;

  List<Item> _itemList = [];
  @override
  List<Item> get itemList => _itemList;
  double get _itemTotalSum =>
      _itemList.fold(0.0, (total, item) => total + item.total);

  Tip? _tip;
  @override
  Tip? get tip => _tip;
  set tip(Tip? value) => _tip = value;

  @override
  void dispose() {
    _localeRepository.removeListener(() => notifyListeners());
    super.dispose();
  }

  Future<void> _loadItemList() async {
    _itemList = await _itemRepository.getItemList();
    if (itemList.isEmpty) addItem();
    notifyListeners();
  }

  Future<void> _loadTip() async {
    final didInitializeTip = await _tipRepository
        .checkIfTipHasBeenInitialized();
    if (didInitializeTip) {
      _tip = await _tipRepository.getTip();
    } else {
      addTip();
    }
    notifyListeners();
  }

  (int index, Item item) _getIndexedItem(String itemId) {
    return _itemList.indexed.firstWhere(
      (indexedItem) => indexedItem.$2.id == itemId,
    );
  }

  @override
  void onItemNameChanged(String itemId, String name) {
    final indexedItem = _getIndexedItem(itemId);
    final updatedItem = indexedItem.$2.copyWith(name: name);
    _itemList[indexedItem.$1] = updatedItem;
    notifyListeners();
    _itemRepository.updateItem(updatedItem).ignore();
  }

  @override
  void onItemPriceChanged(String itemId, String priceText) {
    final price = priceText.currency();
    final indexedItem = _getIndexedItem(itemId);
    final total = price * indexedItem.$2.quantity;
    final updatedItem = indexedItem.$2.copyWith(price: price, total: total);
    _itemList[indexedItem.$1] = updatedItem;
    _updateTipAmountIfPresent();
    notifyListeners();
    _itemRepository.updateItem(updatedItem).ignore();
  }

  @override
  void onItemQuantityChanged(String itemId, String quantityText) {
    final quantity = int.tryParse(quantityText) ?? 0;
    final indexedItem = _getIndexedItem(itemId);
    final total = indexedItem.$2.price * quantity;
    final updatedItem = indexedItem.$2.copyWith(
      quantity: quantity,
      total: total,
    );
    _itemList[indexedItem.$1] = updatedItem;
    _updateTipAmountIfPresent();
    notifyListeners();
    _itemRepository.updateItem(updatedItem).ignore();
  }

  @override
  void onItemTotalChanged(String itemId, String totalText) {
    final total = totalText.currency();
    final indexedItem = _getIndexedItem(itemId);
    final price = indexedItem.$2.quantity > 0
        ? total / indexedItem.$2.quantity
        : 0.0;
    final updatedItem = indexedItem.$2.copyWith(price: price, total: total);
    _itemList[indexedItem.$1] = updatedItem;
    _updateTipAmountIfPresent();
    notifyListeners();
    _itemRepository.updateItem(updatedItem).ignore();
  }

  @override
  void addItem() {
    final newItem = _itemRepository.generateItem();
    _itemList.add(newItem);
    _updateTipAmountIfPresent();
    notifyListeners();
    _itemRepository.createItem(newItem).ignore();
  }

  @override
  void removeItem(String itemId) {
    // Check if item exists
    final itemIndex = itemList.indexWhere((item) => item.id == itemId);
    if (itemIndex == -1) {
      throw ArgumentError(strings.itemIdNotFoundError(itemId));
    }
    // Prevent deletion if this is the only item in the list
    if (itemList.length == 1) {
      throw StateError(strings.itemDeleteForbiddenError);
    }

    _itemList.removeWhere((item) => item.id == itemId);
    _updateTipAmountIfPresent();
    notifyListeners();
    _itemRepository.deleteItem(itemId).ignore();
  }

  @override
  String? validateItemPrice(String? price) => _validators.validatePrice(price);

  @override
  String? validateItemQuantity(String? quantity) => _validators.validateQuantity(quantity);

  @override
  String? validateItemTotal(String? total) => _validators.validatePrice(total);

  double _calculateTipAmount(int percentage) {
    final amount = _itemTotalSum * (percentage / 100);
    return amount;
  }

  int _calculateTipPercentage(double amount) {
    final percentage = (_itemTotalSum > 0)
        ? ((amount / _itemTotalSum) * 100).round()
        : 0;
    return percentage;
  }

  void _updateTipAmountIfPresent() {
    if (_tip != null) {
      final newAmount = _calculateTipAmount(_tip!.percentage);
      final updatedTip = _tip!.copyWith(amount: newAmount);
      _tip = updatedTip;
      _tipRepository.updateTip(updatedTip).ignore();
    }
  }

  @override
  void onTipPercentageChanged(String percentageText) {
    final percentage = int.tryParse(percentageText) ?? 0;
    final amount = _calculateTipAmount(percentage);
    final updatedTip = _tip!.copyWith(percentage: percentage, amount: amount);
    _tip = updatedTip;
    notifyListeners();
    _tipRepository.updateTip(updatedTip).ignore();
  }

  @override
  void onTipAmountChanged(String amountText) {
    final amount = amountText.currency();
    final percentage = _calculateTipPercentage(amount);
    final updatedTip = _tip!.copyWith(percentage: percentage, amount: amount);
    _tip = updatedTip;
    notifyListeners();
    _tipRepository.updateTip(updatedTip).ignore();
  }

  @override
  void addTip() {
    _tip = Tip();
    notifyListeners();
    _tipRepository.createTip(_tip).ignore();
  }

  @override
  void removeTip() {
    _tip = null;
    notifyListeners();
    _tipRepository.deleteTip().ignore();
  }

  @override
  String? validateTipPercentage(String? percentage) =>
      _validators.validateQuantity(percentage);

  @override
  String? validateTipAmount(String? amount) => _validators.validatePrice(amount);

  @override
  double get billTotal => _itemTotalSum + (_tip?.amount ?? 0.0);

  @override
  String get billTotalFirstLine {
    if (_tip != null) {
      return '\$${_itemTotalSum.toStringAsFixed(2)} + \$${_tip!.amount.toStringAsFixed(2)}';
    } else {
      return strings.totalLabel;
    }
  }

  @override
  String get billTotalSecondLine => '\$${billTotal.toStringAsFixed(2)}';

  @override
  void continueToNextStep() {
    // Enable autovalidation to show errors
    _autovalidateMode = AutovalidateMode.always;
    notifyListeners();

    // Validate the form
    if (_formKey.currentState?.validate() ?? false) {
      // All fields are valid, proceed to next step
      // TODO: Implement navigation to next step
    }
    // If validation fails, the form will automatically show error messages
    // and the user stays on the current step
  }
}
